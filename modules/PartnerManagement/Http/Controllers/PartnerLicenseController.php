<?php

namespace Modules\PartnerManagement\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use App\Models\Response;
use Illuminate\Support\Facades\DB;
use Exception;
use Modules\PartnerManagement\Http\Requests\QLMNUpdateRequest;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Transformers\PartnerLicenseTransformer;

class PartnerLicenseController extends Controller
{
    /**
     * Hiển thị danh sách license
     */
    public function index(Request $request)
    {
        $query = PartnerLicense::forAuthCompany()->with('pms_unit')
            ->select('id', 'code', 'month', 'type', 'project_code', 'project_account', 'activated_at', 'expired_at', 'assigned_at', 'status', 'project_unit_ref');

        if ($request->filled('product_id')) {
            $query->where('project_code', $request->input('product_id'));
        }

        if ($request->filled('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->filled('project_account')) {
            $query->where('project_account', 'like', '%' . $request->input('project_account') . '%');
        }

        if ($request->filled('expired_in')) {
            $days = (int) $request->input('expired_in');
            $query->whereDate('expired_at', '<=', now()->addDays($days));
        }

        if ($request->filled('month')) {
            $query->where('month', $request->input('month'));
        }

        if ($request->filled('province') && !$request->filled('district')) {
            $sgd = PMSUnit::where('id', $request->input('province'))->soGiaoDuc()->isActive()->first();
            $units = $sgd->getAllSchools()->pluck('id')->toArray();
            $query->whereIn('project_unit_ref', $units);
        }

        if ($request->filled('district')) {
            $pgd = PMSUnit::where('id', $request->input('district'))->phongGiaoDuc()->isActive()->first();
            $units = $pgd->getDepartmentSchools()->pluck('id')->toArray();
            $query->whereIn('project_unit_ref', $units);
        }

        $licenses = $query->orderBy('updated_at', 'DESC')->paginate(20);
        //dd($licenses->toArray());

        return api_response(true, 'Success', $licenses, 200, null, PartnerLicenseTransformer::class, true);
    }

    /**
     * Kích hoạt license
     */
    public function activate(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $license = PartnerLicense::findOrFail($id);
            $license->status = 'active';
            $license->save();
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $license);
        } catch (Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.ERROR'), null, $e->getMessage());
        }
    }

    /**
     * Khóa license
     */
    public function deactivate(Request $request, $id)
    {
        try {
            DB::beginTransaction();
            $license = PartnerLicense::findOrFail($id);
            $license->status = 'inactive';
            $license->save();
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $license);
        } catch (Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.ERROR'), null, $e->getMessage());
        }
    }

    /**
     * Cập nhật thông tin license
     */
    public function update(QLMNUpdateRequest $request, $id)
    {
        $db1 = \DB::connection(); // default connection
        $db2 = \DB::connection('mysql_pms'); // replace 'mysql2' with your second connection name
        $db1->beginTransaction();
        $db2->beginTransaction();
        try {
            $current_user = auth('partner-api')->user();
            $data = $request->validated();
            
            $license = (new \Modules\PartnerManagement\Jobs\UpdateLicenseCodeForQLMNJob(
                $data['id'],
                $current_user->company_id,
                $data['project_account'],
                $data['activated_at']
            ))->handle();

            if ($license == null) throw new \Exception("Không có giấy phép mã #" . $data['id']);

            if ($license) {
                $unit = (new \Modules\PartnerManagement\Jobs\UpdatePMSAccountJob([
                    'unit_id' => $license['before']['project_unit_ref'],
                    'before_username' => $license['before']['project_account'],
                    'after_username' => $license['after']['project_account'],
                    'activated_at' => $license['after']['activated_at'],
                    'expired_at' => $license['after']['expired_at'],
                ]))->handle();

                if ($unit == null) throw new \Exception("Không tìm thấy đơn vị");

                // Clear cache sau khi update thành công
                \Cache::forget('partner_license_activities_' . $data['id']);
                \Cache::forget('partner_license_unit_' . $license['before']['project_unit_ref']);
                
                $unit['license'] = $license;
                $db1->commit();
                $db2->commit();
                return api_response(true, 'Cập nhật giấy phép thành công!', $unit);
            }
        } catch (Exception $e) {
            
            $db1->rollBack();
            $db2->rollBack();
            dd($e);
            return api_response(false, $e->getMessage(), null, 422);
        }
    }
}
